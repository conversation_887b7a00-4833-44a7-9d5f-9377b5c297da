/** @format */

import { colors } from '@/app/colors';
import React from 'react';
import AIPlaygroundDemo from './AIPlaygroundDemo';
import DynamicCategoryMosaic from './DynamicCategoryMosaic';
import FloatingHeroIslands from './FloatingHeroIslands';
import Footer from './Footer';

interface LandingPageProps {
	onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
	return (
		<div
			className='min-h-screen relative'
			style={{
				background: `
          linear-gradient(180deg,
            rgba(1, 3, 79, 0.2) 0%,
            rgba(163, 247, 181, 0.2) 25%,
            rgba(128, 237, 153, 0.2) 50%,
            rgba(102, 208, 255, 0.2) 75%,
            rgba(51, 194, 255, 0.2) 100%
          ),
          ${colors.neutral.cloudWhite}
        `,
			}}>
			{/* Main Content */}
			<FloatingHeroIslands onGetStarted={onGetStarted} />
			<DynamicCategoryMosaic
				onCategorySelect={(category) =>
					console.log('Selected category:', category)
				}
			/>
			<AIPlaygroundDemo onGetStarted={onGetStarted} />
			<Footer />
		</div>
	);
};

export default LandingPage;
